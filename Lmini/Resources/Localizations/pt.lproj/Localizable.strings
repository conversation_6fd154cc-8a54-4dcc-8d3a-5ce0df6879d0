/* 
  Localizable.strings
  Lmini - Português
*/

// MARK: - App Information
"app_name" = "Aplicativo Lmini";
"app_subtitle" = "Aprimorado com recursos avançados";
"app_info_title" = "Informações do aplicativo";
"app_info_message" = "Lmini - Aplicativo iOS aprimorado com persistência de dados, troca de temas, recursos de rede e belas animações. Versão 2.0";

// MARK: - Navigation
"home_tab" = "Início";
"network_tab" = "Rede";
"settings_tab" = "Configurações";

// MARK: - User Interface
"enter_name_placeholder" = "Digite seu nome";
"hello_greeting" = "Olá, %@! 👋";
"counter_label" = "Contador";
"reset_counter" = "Redefinir contador";

// MARK: - Quick Actions
"quick_actions" = "Ações rápidas";
"settings_action" = "Configurações";
"app_info_action" = "Informações do app";
"network_action" = "Rede";
"stats_action" = "Estatísticas";

// MARK: - Settings
"settings_title" = "Configurações";
"appearance_section" = "Aparência";
"theme_label" = "Tema";
"theme_light" = "Claro";
"theme_dark" = "Escuro";
"theme_system" = "Sistema";
"counter_settings_section" = "Configurações do contador";
"max_counter_value" = "Valor máximo do contador";
"reset_all_settings" = "Redefinir todas as configurações";
"done" = "Concluído";

// MARK: - Network
"network_title" = "Rede";
"network_status" = "Status da rede";
"connected" = "Conectado";
"disconnected" = "Desconectado";
"fetch_data" = "Buscar dados";
"loading" = "Carregando...";
"error_occurred" = "Ocorreu um erro";
"no_data" = "Nenhum dado disponível";

// MARK: - Alerts
"ok" = "OK";
"cancel" = "Cancelar";
"error" = "Erro";
"success" = "Sucesso";
"warning" = "Aviso";

// MARK: - Accessibility
"increment_button" = "Incrementar contador";
"decrement_button" = "Decrementar contador";
"reset_button" = "Redefinir contador para zero";
"settings_button" = "Abrir configurações";
"info_button" = "Mostrar informações do aplicativo";
