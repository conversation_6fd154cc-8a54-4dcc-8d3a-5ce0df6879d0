/* 
  Localizable.strings
  Lmini - English
*/

// MARK: - App Information
"app_name" = "Lmini App";
"app_subtitle" = "Enhanced with advanced features";
"app_info_title" = "App Information";
"app_info_message" = "Lmini - Enhanced iOS app with data persistence, theme switching, network features, and beautiful animations. Version 2.0";

// MARK: - Navigation
"home_tab" = "Home";
"network_tab" = "Network";
"settings_tab" = "Settings";

// MARK: - User Interface
"enter_name_placeholder" = "Enter your name";
"hello_greeting" = "Hello, %@! 👋";
"counter_label" = "Counter";
"reset_counter" = "Reset Counter";

// MARK: - Quick Actions
"quick_actions" = "Quick Actions";
"settings_action" = "Settings";
"app_info_action" = "App Info";
"network_action" = "Network";
"stats_action" = "Stats";

// MARK: - Settings
"settings_title" = "Settings";
"appearance_section" = "Appearance";
"theme_label" = "Theme";
"theme_light" = "Light";
"theme_dark" = "Dark";
"theme_system" = "System";
"counter_settings_section" = "Counter Settings";
"max_counter_value" = "Max Counter Value";
"reset_all_settings" = "Reset All Settings";
"done" = "Done";

// MARK: - Network
"network_title" = "Network";
"network_status" = "Network Status";
"connected" = "Connected";
"disconnected" = "Disconnected";
"fetch_data" = "Fetch Data";
"loading" = "Loading...";
"error_occurred" = "An error occurred";
"no_data" = "No data available";

// MARK: - Alerts
"ok" = "OK";
"cancel" = "Cancel";
"error" = "Error";
"success" = "Success";
"warning" = "Warning";

// MARK: - Accessibility
"increment_button" = "Increment counter";
"decrement_button" = "Decrement counter";
"reset_button" = "Reset counter to zero";
"settings_button" = "Open settings";
"info_button" = "Show app information";
