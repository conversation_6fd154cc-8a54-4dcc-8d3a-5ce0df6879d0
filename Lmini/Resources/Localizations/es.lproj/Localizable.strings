/* 
  Localizable.strings
  Lmini - Español
*/

// MARK: - App Information
"app_name" = "Aplicación Lmini";
"app_subtitle" = "Mejorada con funciones avanzadas";
"app_info_title" = "Información de la aplicación";
"app_info_message" = "Lmini - Aplicación iOS mejorada con persistencia de datos, cambio de temas, funciones de red y hermosas animaciones. Versión 2.0";

// MARK: - Navigation
"home_tab" = "Inicio";
"network_tab" = "Red";
"settings_tab" = "Configuración";

// MARK: - User Interface
"enter_name_placeholder" = "Ingresa tu nombre";
"hello_greeting" = "¡Hola, %@! 👋";
"counter_label" = "Contador";
"reset_counter" = "Reiniciar contador";

// MARK: - Quick Actions
"quick_actions" = "Acciones rápidas";
"settings_action" = "Configuración";
"app_info_action" = "Información de la app";
"network_action" = "Red";
"stats_action" = "Estadísticas";

// MARK: - Settings
"settings_title" = "Configuración";
"appearance_section" = "Apariencia";
"theme_label" = "Tema";
"theme_light" = "Claro";
"theme_dark" = "Oscuro";
"theme_system" = "Sistema";
"counter_settings_section" = "Configuración del contador";
"max_counter_value" = "Valor máximo del contador";
"reset_all_settings" = "Reiniciar toda la configuración";
"done" = "Hecho";

// MARK: - Network
"network_title" = "Red";
"network_status" = "Estado de la red";
"connected" = "Conectado";
"disconnected" = "Desconectado";
"fetch_data" = "Obtener datos";
"loading" = "Cargando...";
"error_occurred" = "Ocurrió un error";
"no_data" = "No hay datos disponibles";

// MARK: - Alerts
"ok" = "Aceptar";
"cancel" = "Cancelar";
"error" = "Error";
"success" = "Éxito";
"warning" = "Advertencia";

// MARK: - Accessibility
"increment_button" = "Incrementar contador";
"decrement_button" = "Decrementar contador";
"reset_button" = "Reiniciar contador a cero";
"settings_button" = "Abrir configuración";
"info_button" = "Mostrar información de la aplicación";
