/* 
  Localizable.strings
  Lmini - Русский
*/

// MARK: - App Information
"app_name" = "Приложение Lmini";
"app_subtitle" = "Улучшенное с расширенными функциями";
"app_info_title" = "Информация о приложении";
"app_info_message" = "Lmini - Улучшенное iOS приложение с сохранением данных, переключением тем, сетевыми функциями и красивыми анимациями. Версия 2.0";

// MARK: - Navigation
"home_tab" = "Главная";
"network_tab" = "Сеть";
"settings_tab" = "Настройки";

// MARK: - User Interface
"enter_name_placeholder" = "Введите ваше имя";
"hello_greeting" = "Привет, %@! 👋";
"counter_label" = "Счетчик";
"reset_counter" = "Сбросить счетчик";

// MARK: - Quick Actions
"quick_actions" = "Быстрые действия";
"settings_action" = "Настройки";
"app_info_action" = "Информация о приложении";
"network_action" = "Сеть";
"stats_action" = "Статистика";

// MARK: - Settings
"settings_title" = "Настройки";
"appearance_section" = "Внешний вид";
"theme_label" = "Тема";
"theme_light" = "Светлая";
"theme_dark" = "Темная";
"theme_system" = "Системная";
"counter_settings_section" = "Настройки счетчика";
"max_counter_value" = "Максимальное значение счетчика";
"reset_all_settings" = "Сбросить все настройки";
"done" = "Готово";

// MARK: - Network
"network_title" = "Сеть";
"network_status" = "Состояние сети";
"connected" = "Подключено";
"disconnected" = "Отключено";
"fetch_data" = "Получить данные";
"loading" = "Загрузка...";
"error_occurred" = "Произошла ошибка";
"no_data" = "Нет доступных данных";

// MARK: - Alerts
"ok" = "ОК";
"cancel" = "Отмена";
"error" = "Ошибка";
"success" = "Успех";
"warning" = "Предупреждение";

// MARK: - Accessibility
"increment_button" = "Увеличить счетчик";
"decrement_button" = "Уменьшить счетчик";
"reset_button" = "Сбросить счетчик до нуля";
"settings_button" = "Открыть настройки";
"info_button" = "Показать информацию о приложении";
