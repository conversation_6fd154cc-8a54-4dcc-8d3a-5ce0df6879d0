// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		F7210CEA2DE86AB6006A3070 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F7210CD42DE86AB4006A3070 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F7210CDB2DE86AB4006A3070;
			remoteInfo = Lmini;
		};
		F7210CF42DE86AB6006A3070 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F7210CD42DE86AB4006A3070 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F7210CDB2DE86AB4006A3070;
			remoteInfo = Lmini;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		F7210CDC2DE86AB4006A3070 /* Lmini.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Lmini.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F7210CE92DE86AB6006A3070 /* LminiTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LminiTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F7210CF32DE86AB6006A3070 /* LminiUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LminiUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		F7210CDE2DE86AB4006A3070 /* Lmini */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Lmini;
			sourceTree = "<group>";
		};
		F7210CEC2DE86AB6006A3070 /* LminiTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LminiTests;
			sourceTree = "<group>";
		};
		F7210CF62DE86AB6006A3070 /* LminiUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LminiUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		F7210CD92DE86AB4006A3070 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7210CE62DE86AB6006A3070 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7210CF02DE86AB6006A3070 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F7210CD32DE86AB4006A3070 = {
			isa = PBXGroup;
			children = (
				F7210CDE2DE86AB4006A3070 /* Lmini */,
				F7210CEC2DE86AB6006A3070 /* LminiTests */,
				F7210CF62DE86AB6006A3070 /* LminiUITests */,
				F7210CDD2DE86AB4006A3070 /* Products */,
			);
			sourceTree = "<group>";
		};
		F7210CDD2DE86AB4006A3070 /* Products */ = {
			isa = PBXGroup;
			children = (
				F7210CDC2DE86AB4006A3070 /* Lmini.app */,
				F7210CE92DE86AB6006A3070 /* LminiTests.xctest */,
				F7210CF32DE86AB6006A3070 /* LminiUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F7210CDB2DE86AB4006A3070 /* Lmini */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F7210CFD2DE86AB6006A3070 /* Build configuration list for PBXNativeTarget "Lmini" */;
			buildPhases = (
				F7210CD82DE86AB4006A3070 /* Sources */,
				F7210CD92DE86AB4006A3070 /* Frameworks */,
				F7210CDA2DE86AB4006A3070 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				F7210CDE2DE86AB4006A3070 /* Lmini */,
			);
			name = Lmini;
			packageProductDependencies = (
			);
			productName = Lmini;
			productReference = F7210CDC2DE86AB4006A3070 /* Lmini.app */;
			productType = "com.apple.product-type.application";
		};
		F7210CE82DE86AB6006A3070 /* LminiTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F7210D002DE86AB6006A3070 /* Build configuration list for PBXNativeTarget "LminiTests" */;
			buildPhases = (
				F7210CE52DE86AB6006A3070 /* Sources */,
				F7210CE62DE86AB6006A3070 /* Frameworks */,
				F7210CE72DE86AB6006A3070 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F7210CEB2DE86AB6006A3070 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F7210CEC2DE86AB6006A3070 /* LminiTests */,
			);
			name = LminiTests;
			packageProductDependencies = (
			);
			productName = LminiTests;
			productReference = F7210CE92DE86AB6006A3070 /* LminiTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F7210CF22DE86AB6006A3070 /* LminiUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F7210D032DE86AB6006A3070 /* Build configuration list for PBXNativeTarget "LminiUITests" */;
			buildPhases = (
				F7210CEF2DE86AB6006A3070 /* Sources */,
				F7210CF02DE86AB6006A3070 /* Frameworks */,
				F7210CF12DE86AB6006A3070 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F7210CF52DE86AB6006A3070 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F7210CF62DE86AB6006A3070 /* LminiUITests */,
			);
			name = LminiUITests;
			packageProductDependencies = (
			);
			productName = LminiUITests;
			productReference = F7210CF32DE86AB6006A3070 /* LminiUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F7210CD42DE86AB4006A3070 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					F7210CDB2DE86AB4006A3070 = {
						CreatedOnToolsVersion = 16.3;
					};
					F7210CE82DE86AB6006A3070 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = F7210CDB2DE86AB4006A3070;
					};
					F7210CF22DE86AB6006A3070 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = F7210CDB2DE86AB4006A3070;
					};
				};
			};
			buildConfigurationList = F7210CD72DE86AB4006A3070 /* Build configuration list for PBXProject "Lmini" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F7210CD32DE86AB4006A3070;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = F7210CDD2DE86AB4006A3070 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F7210CDB2DE86AB4006A3070 /* Lmini */,
				F7210CE82DE86AB6006A3070 /* LminiTests */,
				F7210CF22DE86AB6006A3070 /* LminiUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F7210CDA2DE86AB4006A3070 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7210CE72DE86AB6006A3070 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7210CF12DE86AB6006A3070 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F7210CD82DE86AB4006A3070 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7210CE52DE86AB6006A3070 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7210CEF2DE86AB6006A3070 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F7210CEB2DE86AB6006A3070 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F7210CDB2DE86AB4006A3070 /* Lmini */;
			targetProxy = F7210CEA2DE86AB6006A3070 /* PBXContainerItemProxy */;
		};
		F7210CF52DE86AB6006A3070 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F7210CDB2DE86AB4006A3070 /* Lmini */;
			targetProxy = F7210CF42DE86AB6006A3070 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		F7210CFB2DE86AB6006A3070 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F7210CFC2DE86AB6006A3070 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 43MGF57VPU;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F7210CFE2DE86AB6006A3070 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ai.Lmini;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F7210CFF2DE86AB6006A3070 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ai.Lmini;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F7210D012DE86AB6006A3070 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ai.LminiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Lmini.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Lmini";
			};
			name = Debug;
		};
		F7210D022DE86AB6006A3070 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ai.LminiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Lmini.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Lmini";
			};
			name = Release;
		};
		F7210D042DE86AB6006A3070 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ai.LminiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Lmini;
			};
			name = Debug;
		};
		F7210D052DE86AB6006A3070 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 43MGF57VPU;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ai.LminiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Lmini;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F7210CD72DE86AB4006A3070 /* Build configuration list for PBXProject "Lmini" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F7210CFB2DE86AB6006A3070 /* Debug */,
				F7210CFC2DE86AB6006A3070 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F7210CFD2DE86AB6006A3070 /* Build configuration list for PBXNativeTarget "Lmini" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F7210CFE2DE86AB6006A3070 /* Debug */,
				F7210CFF2DE86AB6006A3070 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F7210D002DE86AB6006A3070 /* Build configuration list for PBXNativeTarget "LminiTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F7210D012DE86AB6006A3070 /* Debug */,
				F7210D022DE86AB6006A3070 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F7210D032DE86AB6006A3070 /* Build configuration list for PBXNativeTarget "LminiUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F7210D042DE86AB6006A3070 /* Debug */,
				F7210D052DE86AB6006A3070 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F7210CD42DE86AB4006A3070 /* Project object */;
}
