//
//  NetworkService.swift
//  Lmini
//
//  Created by MacBook Pro-Leo on 5/30/25.
//

import Foundation
import SwiftUI

// MARK: - Quote Model
struct Quote: Codable, Identifiable {
    let id = UUID()
    let text: String
    let author: String?
    
    enum CodingKeys: String, CodingKey {
        case text = "q"
        case author = "a"
    }
}

// MARK: - Weather Model
struct Weather: Codable {
    let temperature: Double
    let description: String
    let city: String
}

// MARK: - Network Service
class NetworkService: ObservableObject {
    @Published var currentQuote: Quote?
    @Published var isLoadingQuote = false
    @Published var quoteError: String?
    
    @Published var currentWeather: Weather?
    @Published var isLoadingWeather = false
    @Published var weatherError: String?
    
    // Additional properties for NetworkView
    @Published var isConnected = false
    @Published var lastUpdateTime: Date?
    @Published var isLoading = false
    @Published var fetchedData: [String: Any] = [:]
    @Published var hasError = false
    @Published var errorMessage: String?
    
    private let session = URLSession.shared
    
    init() {
        checkConnectivityAsync()
    }
    
    // MARK: - Quote API
    func fetchRandomQuote() {
        isLoadingQuote = true
        isLoading = true
        quoteError = nil
        hasError = false
        
        guard let url = URL(string: "https://zenquotes.io/api/random") else {
            quoteError = "Invalid URL"
            errorMessage = "Invalid URL"
            isLoadingQuote = false
            isLoading = false
            hasError = true
            return
        }
        
        session.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isLoadingQuote = false
                self?.isLoading = false
                self?.lastUpdateTime = Date()
                
                if let error = error {
                    self?.quoteError = error.localizedDescription
                    self?.errorMessage = error.localizedDescription
                    self?.hasError = true
                    return
                }
                
                guard let data = data else {
                    self?.quoteError = "No data received"
                    self?.errorMessage = "No data received"
                    self?.hasError = true
                    return
                }
                
                do {
                    let quotes = try JSONDecoder().decode([Quote].self, from: data)
                    self?.currentQuote = quotes.first
                    self?.fetchedData["quote"] = quotes.first?.text ?? ""
                    self?.hasError = false
                    self?.errorMessage = nil
                } catch {
                    self?.quoteError = "Failed to decode quote: \(error.localizedDescription)"
                    self?.errorMessage = "Failed to decode quote: \(error.localizedDescription)"
                    self?.hasError = true
                }
            }
        }.resume()
    }
    
    // MARK: - Mock Weather API (since we don't have API keys)
    func fetchWeather(for city: String = "San Francisco") {
        isLoadingWeather = true
        isLoading = true
        weatherError = nil
        hasError = false
        
        // Simulate network delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.isLoadingWeather = false
            self?.isLoading = false
            self?.lastUpdateTime = Date()
            
            // Mock weather data
            let mockWeather = Weather(
                temperature: Double.random(in: 15...30),
                description: ["Sunny", "Cloudy", "Rainy", "Partly Cloudy"].randomElement() ?? "Sunny",
                city: city
            )
            
            self?.currentWeather = mockWeather
            self?.fetchedData["weather"] = "\(mockWeather.temperature)°C, \(mockWeather.description)"
            self?.hasError = false
            self?.errorMessage = nil
        }
    }
    
    // MARK: - Connectivity Check
    func checkConnectivity() -> Bool {
        // Simple connectivity check
        guard let url = URL(string: "https://www.google.com") else { return false }
        
        var request = URLRequest(url: url)
        request.timeoutInterval = 5.0
        
        let semaphore = DispatchSemaphore(value: 0)
        var connected = false
        
        session.dataTask(with: request) { _, response, _ in
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200 {
                connected = true
            }
            semaphore.signal()
        }.resume()
        
        semaphore.wait()
        return connected
    }
    
    // MARK: - Async Connectivity Check
    func checkConnectivityAsync() {
        DispatchQueue.global(qos: .background).async { [weak self] in
            let connected = self?.checkConnectivity() ?? false
            DispatchQueue.main.async {
                self?.isConnected = connected
            }
        }
    }
    
    // MARK: - Refresh All Data
    func refreshAllData() {
        checkConnectivityAsync()
        fetchRandomQuote()
        fetchWeather()
    }
    
    // MARK: - Sync Data (Mock)
    func syncUserData(_ settings: UserSettings) {
        // Mock sync functionality
        print("Syncing user data: \(settings.exportSettings())")
        
        // In a real app, you would send this to your backend
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            print("Data sync completed successfully")
        }
    }
}
